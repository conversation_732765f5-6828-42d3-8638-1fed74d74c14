import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// API Configuration
export const API_CONFIG = {
  BASE_URL: 'https://dev.stt-user.acuizen.com',
  JWT_TOKEN: '************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'
// JWT_TOKEN: '******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'
};

// API Service for making authenticated requests
export class ApiService {
  private static baseURL = API_CONFIG.BASE_URL;
  private static token = API_CONFIG.JWT_TOKEN;

  // Standard filter parameters for incident API calls
  private static getIncidentFilters() {
    return {
      include: [
        'locationOne',
        'locationTwo',
        'locationThree',
        'locationFour',
        'locationFive',
        'locationSix',
        'incidentCircumstanceCategory',
        'incidentCircumstanceDescription',
        'incidentCircumstanceType',
        'lighting',
        'riskCategory',
        'surfaceCondition',
        'surfaceType',
        'workActivity',
        'reviewer',
        'user'
      ]
    };
  }

  static async get(endpoint: string) {
    try {
      console.log(`Making API request to: ${this.baseURL}${endpoint}`);
      const response = await fetch(`${this.baseURL}${endpoint}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.token}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        mode: 'cors',
      });

      console.log(`Response status: ${response.status}`);
      console.log(`Response headers:`, response.headers);

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`HTTP error! status: ${response.status}, body: ${errorText}`);
        throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);
      }

      const data = await response.json();
      console.log('API response data:', data);
      return data;
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  }

  static async post(endpoint: string, data: any) {
    try {
      const response = await fetch(`${this.baseURL}${endpoint}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  }

  static async delete(endpoint: string) {
    try {
      console.log(`Making DELETE request to: ${this.baseURL}${endpoint}`);
      const response = await fetch(`${this.baseURL}${endpoint}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${this.token}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        mode: 'cors',
      });

      console.log(`DELETE response status: ${response.status}`);

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`DELETE HTTP error! status: ${response.status}, body: ${errorText}`);
        throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);
      }

      // Some DELETE endpoints might return empty response
      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        const data = await response.json();
        console.log('DELETE response data:', data);
        return data;
      } else {
        console.log('DELETE request successful - no JSON response');
        return { success: true };
      }
    } catch (error) {
      console.error('DELETE request failed:', error);
      throw error;
    }
  }

  // Specific method to get current user data
  static async getCurrentUser() {
    return this.get('/users/me');
  }

  // Location API methods - Hierarchical location endpoints
  static async getLocationOnes() {
    return this.get('/location-ones');
  }

  static async getLocationTwos(locationOneId: string) {
    return this.get(`/location-ones/${locationOneId}/location-twos`);
  }

  static async getLocationThrees(locationTwoId: string) {
    return this.get(`/location-twos/${locationTwoId}/location-threes`);
  }

  static async getLocationFours(locationThreeId: string) {
    return this.get(`/location-threes/${locationThreeId}/location-fours`);
  }

  static async getLocationFives(locationFourId: string) {
    return this.get(`/location-fours/${locationFourId}/location-fives`);
  }

  static async getLocationSixes(locationFiveId: string) {
    return this.get(`/location-fives/${locationFiveId}/location-sixes`);
  }

  // Create new incident
  static async createIncident(incidentData: any) {
    return this.post('/report-incidents', incidentData);
  }

  // Delete incident by ID
  static async deleteIncident(incidentId: string) {
    console.log(`🗑️ ApiService.deleteIncident: Deleting incident with ID: ${incidentId}`);
    return this.delete(`/new-report-incidents/${incidentId}`);
  }

  // Get incident circumstance categories for incident type dropdown
  static async getIncidentCircumstanceCategories() {
    return this.get('/incident-circumstance-categories');
  }

  // Get incident circumstance types for incident category dropdown
  static async getIncidentCircumstanceTypes() {
    return this.get('/incident-circumstance-types');
  }

  // Get surface types for surface type dropdown
  static async getSurfaceTypes() {
    return this.get('/surface-types');
  }

  // Get surface conditions for surface condition dropdown
  static async getSurfaceConditions() {
    return this.get('/surface-conditions');
  }

  // Get lightings for lighting dropdown
  static async getLightings() {
    return this.get('/lightings');
  }

  // Get weather conditions for weather condition dropdown
  static async getWeatherConditions() {
    return this.get('/weather-conditions');
  }

  // Get dynamic titles for location labels
  static async getDynamicTitles() {
    return this.get('/dynamic-titles');
  }

  // Get users for incident reviewer based on location and mode
  static async getUsers(params: {
    locationOneId?: string;
    locationTwoId?: string;
    locationThreeId?: string;
    locationFourId?: string;
    mode: string;
  }) {
    return this.post('/users/get_users', params);
  }

  // Get incidents for reporter actions using the dedicated reporter API
  static async getReporterIncidents() {
    console.log('🔍 ApiService.getReporterIncidents: Fetching reporter incidents using dedicated API...');

    // Use the dedicated reporter-specific endpoint
    // Get standard filter parameters
    const uriString = this.getIncidentFilters();
    console.log('🔧 ApiService.getReporterIncidents: Using filters:', uriString);

    // Convert filters to query string using filter parameter
    const url = `/get-new-report-incidents-reporter?filter=${encodeURIComponent(JSON.stringify(uriString))}`;

    console.log(`🔗 ApiService.getReporterIncidents: Full URL: ${this.baseURL}${url}`);
    return this.get(url);
  }

  // Get incidents for reviewer actions using /actions/INCIDENT API endpoint
  static async getReviewerActions() {
    console.log('🔍 ApiService.getReviewerActions: Fetching reviewer action incidents...');

    const url = '/actions/get/INCIDENT';
    console.log(`🔗 ApiService.getReviewerActions: Full URL: ${this.baseURL}${url}`);
    return this.get(url);
  }

  // Get all incidents using the new endpoint
  static async getAllIncidents() {
    console.log('🔍 ApiService.getAllIncidents: Fetching all incidents...');

    // Get standard filter parameters
    const uriString = this.getIncidentFilters();
    console.log('🔧 ApiService.getAllIncidents: Using filters:', uriString);

    // Convert filters to query string using filter parameter
    const url = `/all-report-incidents?filter=${encodeURIComponent(JSON.stringify(uriString))}`;

    console.log(`🔗 ApiService.getAllIncidents: Full URL: ${this.baseURL}${url}`);
    return this.get(url);
  }

  // Get all incidents - try common endpoint patterns with filters (legacy method)
  static async getIncidents() {
    console.log('🔍 ApiService.getIncidents: Starting incident fetch...');
    console.log('🌐 Base URL:', this.baseURL);

    // First try the new all incidents endpoint
    try {
      console.log('🔄 ApiService: Trying new /all-new-report-incidents endpoint');
      const result = await this.getAllIncidents();
      console.log('✅ ApiService: SUCCESS with /all-new-report-incidents! Response:', result);
      return result;
    } catch (error) {
      console.log('❌ ApiService: /all-new-report-incidents failed, trying fallback endpoints');
    }

    // Get standard filter parameters
    const uriString = this.getIncidentFilters();
    console.log('🔧 ApiService: Using filters:', uriString);

    const endpoints = [
      '/new-report-incidents',
      '/report-incidents',
      '/incident-reports',
      '/list-incidents'
    ];

    for (const endpoint of endpoints) {
      try {
        console.log(`🔄 ApiService: Trying GET ${this.baseURL}${endpoint} with filters`);

        // Convert filters to query string using filter parameter
        const url = `${endpoint}?filter=${encodeURIComponent(JSON.stringify(uriString))}`;

        console.log(`🔗 ApiService: Full URL: ${this.baseURL}${url}`);
        const result = await this.get(url);
        console.log(`✅ ApiService: SUCCESS with ${endpoint}! Response:`, result);
        return result;
      } catch (error) {
        const errorMsg = error instanceof Error ? error.message : String(error);
        console.log(`❌ ApiService: FAILED ${endpoint} - ${errorMsg}`);

        // Log more details for debugging
        if (error instanceof Error && 'status' in error) {
          console.log(`   Status: ${(error as any).status}`);
        }
      }
    }

    console.error('🚫 ApiService: ALL incident endpoints failed');
    console.log('💡 ApiService: This means the API server does not have a GET endpoint for retrieving incidents');
    console.log('💡 ApiService: The /new-report-incidents endpoint is likely POST-only for creating incidents');
    throw new Error('No incident retrieval endpoint found. API server needs a GET endpoint like /incidents or /get-incidents to fetch incident data.');
  }
}
